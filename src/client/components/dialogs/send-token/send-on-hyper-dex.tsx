import {
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Wrapper,
} from "@/client/components/ui";
import useDialogStore from "@/client/hooks/use-dialog-store";

export const SendOnHyperDex = () => {
  const { closeDialog } = useDialogStore();
  return (
    <DialogWrapper>
      <DialogHeader
        title="Send on Hyperliquid DEX"
        onClose={() => {
          closeDialog();
        }}
      />
      <DialogContent>
        <div>SendOnHyperDex</div>
      </DialogContent>
    </DialogWrapper>
  );
};
